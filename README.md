# 热点板块优质股票筛选系统

基于tushare数据接口的热点板块优质股票筛选工具，通过多维度指标分析，自动筛选出热点板块中的优质股票，构建投资股票池。

## 功能特点

### 🔥 热点板块识别
就像找当红明星一样，我们要找最火的板块：
- 自动获取概念板块数据 - 看看市场上有哪些概念在炒作
- 计算板块平均涨跌幅和上涨比例 - 哪个板块涨得最好，涨的股票最多
- 识别表现优异的热点板块 - 挑出最火的几个板块重点关注

### 📊 多维度股票评分
我们从两个角度给股票打分，就像考试一样，满分100分：

- **📈 财务指标评分 (60分) - 看公司基本面**
  - PE估值评分 (15分) - 股票贵不贵？
  - PB估值评分 (15分) - 有没有安全边际？
  - ROE盈利能力评分 (15分) - 公司会不会赚钱？
  - 流动比率评分 (15分) - 公司会不会缺钱？

- **📊 技术指标评分 (40分) - 看市场表现**
  - 短期趋势评分 (10分) - 最近5天涨得怎么样？
  - 中期趋势评分 (10分) - 最近20天表现如何？
  - 成交量评分 (10分) - 有没有资金关注？
  - RSI评分 (10分) - 涨得是否过头了？

### 📈 智能筛选
就像选班长一样，我们要选最优秀的：
- 综合评分排序 - 把所有股票按分数从高到低排队
- 可配置筛选条件 - 可以调整选股的严格程度
- 自动生成股票池 - 最终给你一份优质股票清单

## 安装依赖

```bash
# 安装Python依赖
uv add tushare pandas numpy

# 可选：安装分析报告依赖
uv add matplotlib seaborn
```

## 使用方法

### 1. 配置tushare token
在 `main.py` 中设置你的tushare token：
```python
my_token = 'your_tushare_token_here'
```

### 2. 运行股票筛选
```bash
python main.py
```

### 3. 生成分析报告
```bash
python stock_analysis_report.py
```

## 输出结果

### 控制台输出
- 热点板块列表及涨幅
- 股票分析过程
- 筛选结果汇总
- 按板块分布统计

### CSV文件
生成 `hot_sector_stocks_YYYYMMDD.csv` 文件，包含：
- 股票代码和名称
- 所属行业和板块
- 综合评分
- 财务指标 (PE、PB、ROE、流动比率)
- 技术指标 (涨跌幅、成交量比率、RSI)

### 分析报告
详细的股票池分析报告，包含：
- 基本统计信息
- 板块分布
- 评分分布
- 财务和技术指标统计
- 推荐股票列表
- 风险提示

## 指标详解 📚

### 财务指标 (基本面分析)

#### 💰 PE市盈率 (Price-to-Earnings Ratio)
**含义**: 股价除以每股收益，反映投资者愿意为每1元利润支付多少钱
**作用**: 判断股票是否被高估或低估
- **0-15倍**: 便宜货！股票被严重低估，可能是捡漏机会 (15分)
- **15-25倍**: 合理价位，估值适中 (10分)
- **25-40倍**: 有点贵了，但还能接受 (5分)
- **40倍以上**: 太贵了！可能存在泡沫风险 (0分)

#### 📊 PB市净率 (Price-to-Book Ratio)
**含义**: 股价除以每股净资产，反映股价相对于公司净资产的倍数
**作用**: 衡量股票的安全边际
- **0-2倍**: 超级安全！即使公司倒闭，资产也能覆盖股价 (15分)
- **2-3倍**: 比较安全，有一定保障 (10分)
- **3-5倍**: 一般般，风险开始增加 (5分)
- **5倍以上**: 风险较高，要小心了 (0分)

#### 🎯 ROE净资产收益率 (Return on Equity)
**含义**: 公司用股东的钱赚了多少钱，反映管理层的赚钱能力
**作用**: 判断公司盈利能力强弱
- **≥15%**: 赚钱机器！管理层很会做生意 (15分)
- **≥10%**: 不错的公司，盈利能力良好 (10分)
- **≥5%**: 一般般，勉强及格 (5分)
- **<5%**: 赚钱能力差，要谨慎 (0分)

#### 💧 流动比率 (Current Ratio)
**含义**: 流动资产除以流动负债，反映公司短期偿债能力
**作用**: 判断公司会不会突然缺钱
- **≥2**: 钱很充足，不用担心资金链断裂 (15分)
- **≥1.5**: 资金比较充足，基本安全 (10分)
- **≥1**: 勉强够用，需要关注 (5分)
- **<1**: 危险！可能面临资金链断裂 (0分)

### 技术指标 (市场表现)

#### 📈 5日涨跌幅
**含义**: 最近5个交易日的股价变化幅度
**作用**: 反映短期市场情绪和资金关注度
- **>5%**: 短期强势，资金追捧 (10分)
- **>0%**: 小幅上涨，表现平稳 (5分)
- **≤0%**: 短期走弱，需要观察 (0分)

#### 📊 20日涨跌幅
**含义**: 最近20个交易日的股价变化幅度
**作用**: 反映中期趋势和投资者信心
- **>10%**: 中期强势，趋势向好 (10分)
- **>0%**: 中期上涨，基本面可能在改善 (5分)
- **≤0%**: 中期走弱，要小心了 (0分)

#### 🔊 成交量比率
**含义**: 最近5日平均成交量 ÷ 前期平均成交量
**作用**: 判断资金关注度和市场活跃程度
- **>1.5**: 成交量放大，资金大量关注 (10分)
- **>1.2**: 成交量温和放大，有资金进入 (5分)
- **≤1.2**: 成交量平淡，缺乏关注 (0分)

#### ⚖️ RSI相对强弱指标 (Relative Strength Index)
**含义**: 衡量股价涨跌动能的技术指标，范围0-100
**作用**: 判断股票是否超买或超卖
- **30-70**: 正常区间，买卖力量相对均衡 (10分)
- **20-80**: 可接受区间，略有偏向但不极端 (5分)
- **<20**: 超卖区，可能反弹但也可能继续下跌 (0分)
- **>80**: 超买区，可能回调风险较大 (0分)

## 评分标准总结

**总分100分 = 财务指标60分 + 技术指标40分**

- **80分以上**: 🌟🌟🌟 优质股票，值得重点关注
- **60-80分**: 🌟🌟 良好股票，可以考虑配置
- **40-60分**: 🌟 一般股票，需要谨慎评估
- **40分以下**: ❌ 较差股票，建议回避

## 风险提示 ⚠️

**说人话的风险提醒**：
- 🎓 **这只是个学习工具** - 就像练车，不能直接上高速
- 💸 **股市有风险** - 赚钱不容易，亏钱很简单，别把全部身家都投进去
- 📊 **过去不等于未来** - 昨天涨得好，不代表明天还会涨
- 🧠 **多动脑子** - 不要只看这一个工具，要多方面分析
- 👨‍💼 **问问专业人士** - 有条件的话，咨询一下专业的投资顾问

**特别提醒**：
- 🚨 **高PE股票** - PE超过50倍的要小心，可能是泡沫
- 📉 **负ROE股票** - 连钱都赚不到的公司，投资要谨慎
- ⭐ **ST股票** - 带ST标记的股票有退市风险，新手别碰

## 系统要求

- Python 3.8+
- tushare Pro账户和token
- 稳定的网络连接

## 注意事项

1. **API限制**: tushare有调用频率限制，程序已加入延时控制
2. **数据准确性**: 依赖tushare数据质量，建议交叉验证
3. **市场变化**: 股票市场瞬息万变，建议及时更新数据
4. **个股风险**: 注意ST股票等特殊风险

## 更新日志

### v1.0.0 (2025-08-26)
- 初始版本发布
- 实现热点板块识别
- 实现多维度股票评分
- 生成股票池和分析报告

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！