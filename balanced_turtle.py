import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置Tushare Token
your_tushare_token = os.getenv('TUSHARE_TOKEN', '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
ts.set_token(your_tushare_token)
pro = ts.pro_api()

class BalancedTurtleStrategy:
    """平衡版海龟策略 - 在胜率、频率、收益间找平衡"""
    
    def __init__(self, initial_capital=30000, risk_per_trade=0.01, max_units=4):
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_units = max_units
        self.commission_rate = 0.0003
        self.slippage = 0.001
        
        # 策略参数
        self.sys1_entry = 20
        self.sys1_exit = 10
        self.atr_period = 20
        
        # 平衡过滤参数 - 关键调整
        self.enable_filters = True
        self.min_breakout_strength = 0.15     # 适中的突破强度要求
        self.trend_ma_period = 30              # 中期趋势
        self.min_volume_ratio = 1.2           # 适中的成交量要求
        
        self.reset_state()
    
    def reset_state(self):
        self.cash = self.initial_capital
        self.position = 0
        self.units = 0
        self.entry_prices = []
        self.last_entry_price = 0
        self.trade_log = []
        self.equity_curve = []
        self.current_n = 0
    
    def calculate_indicators(self, df):
        # 基本指标
        df['prev_close'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
        df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
        df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
        df['N'] = df['TR'].rolling(window=self.atr_period).mean()
        
        # 信号
        df['sys1_entry_signal'] = df['high'].rolling(window=self.sys1_entry).max().shift(1)
        df['sys1_exit_signal'] = df['low'].rolling(window=self.sys1_exit).min().shift(1)
        
        # 过滤指标
        if self.enable_filters:
            # 趋势
            df['ma_trend'] = df['close'].rolling(window=self.trend_ma_period).mean()
            df['above_trend'] = (df['close'] > df['ma_trend']).astype(int)
            
            # 突破强度
            df['breakout_strength'] = (df['high'] - df['sys1_entry_signal']) / df['N']
            
            # 成交量（模拟）
            np.random.seed(42)
            base_vol = 1000000
            df['vol'] = base_vol * (1 + 0.3 * np.random.randn(len(df)))
            df['vol'] = df['vol'].abs()
            df['vol_ma20'] = df['vol'].rolling(window=20).mean()
            df['vol_ratio'] = df['vol'] / df['vol_ma20']
        
        return df
    
    def check_filters(self, row):
        if not self.enable_filters:
            return True, "无过滤"
        
        # 趋势过滤
        if row.get('above_trend', 1) == 0:
            return False, f"价格低于{self.trend_ma_period}日均线"
        
        # 突破强度过滤
        strength = row.get('breakout_strength', 0)
        if pd.notna(strength) and strength < self.min_breakout_strength:
            return False, f"突破强度不足({strength:.2f}N<{self.min_breakout_strength}N)"
        
        # 成交量过滤
        vol_ratio = row.get('vol_ratio', 1.0)
        if vol_ratio < self.min_volume_ratio:
            return False, f"成交量不足({vol_ratio:.1f}<{self.min_volume_ratio})"
        
        return True, f"趋势✓|强度✓({strength:.2f}N)|成交量✓({vol_ratio:.1f})"
    
    def calculate_position_size(self, current_n, current_price):
        if current_n <= 0:
            return 0
        current_equity = self.cash + self.position * current_price
        risk_amount = current_equity * self.risk_per_trade
        unit_value = risk_amount / current_n
        shares = int(unit_value / current_price / 100) * 100
        return max(shares, 0)
    
    def should_add_position(self, current_high):
        if self.units >= self.max_units or self.units == 0:
            return False, 0
        next_add_price = self.last_entry_price + 0.5 * self.current_n
        if current_high >= next_add_price:
            return True, next_add_price
        return False, 0
    
    def execute_trade(self, date, action, price, shares, reason=""):
        if action == 'BUY':
            cost = shares * price * (1 + self.commission_rate + self.slippage)
            if cost <= self.cash:
                self.cash -= cost
                self.position += shares
                self.units += 1
                self.entry_prices.append(price)
                self.last_entry_price = price
                
                self.trade_log.append({
                    'date': date, 'action': action, 'price': price, 
                    'shares': shares, 'units': self.units, 'cash': self.cash,
                    'reason': reason
                })
                print(f"买入: {date.strftime('%Y-%m-%d')}, 价格: {price:.2f}, "
                      f"股数: {shares}, 单位: {self.units}, 原因: {reason}")
                return True
        
        elif action == 'SELL':
            if self.position > 0:
                avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
                total_return = (price - avg_entry_price) / avg_entry_price if avg_entry_price > 0 else 0
                profit_loss = self.position * (price - avg_entry_price)
                
                proceeds = self.position * price * (1 - self.commission_rate - self.slippage)
                self.cash += proceeds
                
                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': self.position, 'units': self.units, 'cash': self.cash,
                    'reason': reason, 'profit_loss': profit_loss, 'return_rate': total_return
                })
                
                print(f"卖出: {date.strftime('%Y-%m-%d')}, 价格: {price:.2f}, "
                      f"收益率: {total_return*100:+.2f}%, 盈亏: {profit_loss:+.2f}元, 原因: {reason}")
                
                self.position = 0
                self.units = 0
                self.entry_prices = []
                self.last_entry_price = 0
                return True
        
        return False
    
    def backtest(self, df):
        print("开始平衡版海龟策略回测...")
        self.reset_state()
        df = self.calculate_indicators(df)
        
        for i, row in df.iterrows():
            if pd.isna(row['N']) or pd.isna(row['sys1_entry_signal']) or pd.isna(row['sys1_exit_signal']):
                current_equity = self.cash + self.position * row['close']
                self.equity_curve.append(current_equity)
                continue
            
            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            self.current_n = row['N']
            
            # 止损检查
            if self.position > 0:
                avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
                stop_loss_price = avg_entry_price - 2 * self.current_n
                if current_low <= stop_loss_price:
                    self.execute_trade(i, 'SELL', stop_loss_price, 0, "2N止损")
            
            # 出场信号检查
            elif self.position > 0 and current_low <= row['sys1_exit_signal']:
                self.execute_trade(i, 'SELL', row['sys1_exit_signal'], 0, "系统1出场")
            
            # 加仓检查
            elif self.position > 0:
                should_add, add_price = self.should_add_position(current_high)
                if should_add:
                    shares = self.calculate_position_size(self.current_n, add_price)
                    if shares > 0:
                        self.execute_trade(i, 'BUY', add_price, shares, "加仓")
            
            # 入场信号检查
            elif self.position == 0 and current_high > row['sys1_entry_signal']:
                filter_passed, filter_reason = self.check_filters(row)
                if filter_passed:
                    shares = self.calculate_position_size(self.current_n, row['sys1_entry_signal'])
                    if shares > 0:
                        reason = f"系统1入场 ({filter_reason})"
                        self.execute_trade(i, 'BUY', row['sys1_entry_signal'], shares, reason)
                else:
                    print(f"信号被过滤: {i.strftime('%Y-%m-%d')} - {filter_reason}")
            
            # 记录每日净资产
            current_equity = self.cash + self.position * current_close
            self.equity_curve.append(current_equity)
        
        return df
    
    def print_results(self):
        if not self.equity_curve:
            print("错误: 没有回测数据")
            return
        
        final_equity = self.equity_curve[-1]
        total_return = (final_equity - self.initial_capital) / self.initial_capital * 100
        
        print(f"\n{'='*50}")
        print(f"平衡版海龟策略回测结果")
        print(f"{'='*50}")
        print(f"初始资金: {self.initial_capital:,.2f} 元")
        print(f"最终资产: {final_equity:,.2f} 元")
        print(f"总收益率: {total_return:.2f}%")
        
        # 计算胜率
        buy_trades = [t for t in self.trade_log if t['action'] == 'BUY']
        sell_trades = [t for t in self.trade_log if t['action'] == 'SELL']
        winning_trades = [t for t in sell_trades if t.get('return_rate', 0) > 0]
        
        print(f"交易次数: {len(buy_trades)}")
        if len(sell_trades) > 0:
            win_rate = len(winning_trades) / len(sell_trades) * 100
            print(f"胜率: {win_rate:.2f}%")
        
        # 性能指标
        if len(self.equity_curve) > 1:
            equity_series = pd.Series(self.equity_curve)
            returns = equity_series.pct_change().dropna()
            
            annual_return = returns.mean() * 252
            annual_volatility = returns.std() * np.sqrt(252)
            
            peak = equity_series.cummax()
            drawdown = (peak - equity_series) / peak
            max_drawdown = drawdown.max()
            
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
            
            print(f"年化收益率: {annual_return*100:.2f}%")
            print(f"年化波动率: {annual_volatility*100:.2f}%")
            print(f"最大回撤: {max_drawdown*100:.2f}%")
            print(f"夏普比率: {sharpe_ratio:.2f}")


def get_data():
    try:
        df = pro.fund_daily(ts_code='510330.SH', start_date='20150701', end_date='20250827')
        print(f"成功获取数据，共{len(df)}行")
        
        if df.empty:
            print("获取的数据为空")
            return None
            
        df = df.sort_values('trade_date')
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        print(f"日期范围: {df.index.min()} 到 {df.index.max()}")
        
        return df
        
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None


def test_different_settings():
    """测试不同参数设置找到最佳平衡点"""
    df = get_data()
    if df is None:
        return

    # 测试配置：[突破强度, 趋势周期, 成交量要求, 描述]
    test_configs = [
        [0.10, 20, 1.1, "宽松过滤"],
        [0.15, 30, 1.2, "平衡过滤"],
        [0.20, 40, 1.3, "严格过滤"],
        [0.25, 50, 1.5, "很严格过滤"],
    ]

    results = []

    for strength, ma_period, vol_ratio, desc in test_configs:
        print(f"\n{'='*60}")
        print(f"测试配置: {desc}")
        print(f"突破强度: {strength}N, 趋势周期: {ma_period}日, 成交量: {vol_ratio}倍")
        print(f"{'='*60}")

        turtle = BalancedTurtleStrategy(initial_capital=30000, risk_per_trade=0.01, max_units=4)
        turtle.min_breakout_strength = strength
        turtle.trend_ma_period = ma_period
        turtle.min_volume_ratio = vol_ratio

        df_copy = df.copy()
        turtle.backtest(df_copy)

        # 收集结果
        final_equity = turtle.equity_curve[-1] if turtle.equity_curve else 30000
        total_return = (final_equity - 30000) / 30000 * 100

        buy_trades = [t for t in turtle.trade_log if t['action'] == 'BUY']
        sell_trades = [t for t in turtle.trade_log if t['action'] == 'SELL']
        winning_trades = [t for t in sell_trades if t.get('return_rate', 0) > 0]

        win_rate = len(winning_trades) / len(sell_trades) * 100 if len(sell_trades) > 0 else 0

        # 计算夏普比率
        if len(turtle.equity_curve) > 1:
            equity_series = pd.Series(turtle.equity_curve)
            returns = equity_series.pct_change().dropna()
            annual_return = returns.mean() * 252
            annual_volatility = returns.std() * np.sqrt(252)
            sharpe_ratio = (annual_return - 0.03) / annual_volatility if annual_volatility > 0 else 0
        else:
            sharpe_ratio = 0

        results.append({
            'config': desc,
            'total_return': total_return,
            'win_rate': win_rate,
            'trade_count': len(buy_trades),
            'sharpe_ratio': sharpe_ratio
        })

        turtle.print_results()

    # 打印对比结果
    print(f"\n{'='*80}")
    print(f"各配置对比结果")
    print(f"{'='*80}")
    print(f"{'配置':<12} {'总收益率':<10} {'胜率':<8} {'交易次数':<8} {'夏普比率':<8}")
    print(f"{'-'*60}")

    for result in results:
        print(f"{result['config']:<12} {result['total_return']:>8.2f}% "
              f"{result['win_rate']:>6.1f}% {result['trade_count']:>6d} "
              f"{result['sharpe_ratio']:>8.2f}")

    # 找出最佳平衡点
    print(f"\n💡 分析建议:")

    # 按综合评分排序（收益率 + 胜率 + 夏普比率 - 交易频率惩罚）
    for result in results:
        score = (result['total_return'] * 0.4 +
                result['win_rate'] * 0.3 +
                result['sharpe_ratio'] * 100 * 0.2 +
                min(result['trade_count'], 20) * 0.1)  # 交易次数适中最好
        result['score'] = score

    best_config = max(results, key=lambda x: x['score'])
    print(f"推荐配置: {best_config['config']}")
    print(f"综合评分: {best_config['score']:.2f}")


def main():
    test_different_settings()


if __name__ == "__main__":
    main()
