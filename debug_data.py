import tushare as ts
import pandas as pd

# 设置token
my_token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(my_token)

def debug_ths_member():
    """调试ths_member返回的数据结构"""
    
    # 测试一个概念板块
    concept_code = '885531.TI'  # 光伏概念
    
    print(f"正在获取板块 {concept_code} 的成分股...")
    
    try:
        members = pro.ths_member(ts_code=concept_code)
        
        print(f"返回数据形状: {members.shape}")
        print(f"列名: {members.columns.tolist()}")
        
        if not members.empty:
            print("\n前10行数据:")
            print(members.head(10))
            
            print(f"\nts_code列的前10个值:")
            for i, code in enumerate(members['ts_code'].head(10)):
                print(f"{i+1}. {code}")
                
        else:
            print("返回数据为空")
            
    except Exception as e:
        print(f"获取数据失败: {e}")

if __name__ == "__main__":
    debug_ths_member()
