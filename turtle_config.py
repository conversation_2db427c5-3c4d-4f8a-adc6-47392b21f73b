"""
海龟策略配置文件
"""

# 策略参数配置
STRATEGY_CONFIG = {
    # 基本参数
    'initial_capital': 30000,      # 初始资金
    'risk_per_trade': 0.01,        # 每笔交易风险比例 (1%)
    'max_units': 4,                # 最大持仓单位数
    'commission_rate': 0.0003,     # 手续费率 (万3)
    'slippage': 0.001,             # 滑点 (0.1%)
    
    # 海龟策略参数
    'sys1_entry': 20,              # 系统1入场突破天数
    'sys1_exit': 10,               # 系统1出场突破天数
    'sys2_entry': 55,              # 系统2入场突破天数
    'sys2_exit': 20,               # 系统2出场突破天数
    'atr_period': 20,              # ATR计算周期
    'add_position_threshold': 0.5,  # 加仓阈值 (0.5N)
    'stop_loss_multiple': 2.0,     # 止损倍数 (2N)
}

# 数据配置
DATA_CONFIG = {
    'symbol': '510330.SH',         # 交易标的 (沪深300ETF)
    'start_date': '20240701',      # 开始日期
    'end_date': '20250827',        # 结束日期
}

# 风险管理配置
RISK_CONFIG = {
    'max_drawdown_limit': 0.20,    # 最大回撤限制 (20%)
    'max_consecutive_losses': 5,    # 最大连续亏损次数
    'position_size_limit': 0.25,   # 单笔交易最大仓位 (25%)
}

# 回测配置
BACKTEST_CONFIG = {
    'benchmark': '510330.SH',      # 基准指数
    'plot_results': True,          # 是否绘制结果图表
    'save_results': True,          # 是否保存结果
    'output_dir': './results',     # 结果输出目录
}

# 优化参数范围（用于参数优化）
OPTIMIZATION_RANGES = {
    'sys1_entry': [15, 20, 25, 30],
    'sys1_exit': [8, 10, 12, 15],
    'risk_per_trade': [0.005, 0.01, 0.015, 0.02],
    'max_units': [2, 3, 4, 5],
    'stop_loss_multiple': [1.5, 2.0, 2.5, 3.0],
}
