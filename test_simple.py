import tushare as ts
import pandas as pd
import random
from datetime import datetime

# 设置token
my_token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
pro = ts.pro_api(my_token)

def get_simple_hot_stocks():
    """简化版本：直接获取热点板块的龙头股票"""
    print("=" * 60)
    print("🔥 实时热点板块龙头股票筛选系统")
    print("=" * 60)
    
    # 预定义一些热门概念板块
    hot_concepts = [
        {'code': '885531.TI', 'name': '光伏概念'},
        {'code': '885806.TI', 'name': '华为概念'},
        {'code': '885977.TI', 'name': '国资云'},
        {'code': '886085.TI', 'name': 'AI眼镜'},
        {'code': '885910.TI', 'name': '拼多多概念'},
        {'code': '886050.TI', 'name': '算力租赁'},
        {'code': '886004.TI', 'name': '虚拟电厂'},
        {'code': '885825.TI', 'name': '冰雪产业'},
    ]
    
    all_stocks = []
    
    for concept in hot_concepts:
        print(f"\n📊 正在分析板块: {concept['name']}")
        
        try:
            # 获取板块成分股
            members = pro.ths_member(ts_code=concept['code'])
            
            if members.empty:
                print(f"  ❌ 无法获取 {concept['name']} 成分股")
                continue
            
            print(f"  📈 板块包含 {len(members)} 只股票")
            
            # 只分析前3只股票作为龙头
            stock_count = 0
            for _, stock in members.iterrows():
                if stock_count >= 3:  # 只要3只龙头股
                    break

                try:
                    stock_code = stock['con_code']  # 使用con_code列获取股票代码
                    stock_name = stock['con_name']  # 使用con_name列获取股票名称

                    print(f"  🔍 正在分析股票: {stock_name} ({stock_code})")

                    # 获取股票基本信息
                    basic_info = pro.stock_basic(ts_code=stock_code)

                    if basic_info.empty:
                        print(f"  ⚠️  {stock_code} 基本信息为空，使用默认信息")
                        industry = "未知"
                    else:
                        industry = basic_info.iloc[0].get('industry', '未知')

                    stock_count += 1

                    # 模拟评分和市值数据
                    market_value = random.randint(100, 2000)  # 100-2000亿市值
                    score = 60 + random.randint(-10, 30)  # 50-90分

                    # 模拟技术指标
                    pct_chg_5d = random.uniform(-5, 8)
                    pct_chg_20d = random.uniform(-10, 15)

                    stock_data = {
                        'ts_code': stock_code,
                        'name': stock_name,
                        'sector': concept['name'],
                        'industry': industry,
                        'score': score,
                        'market_value': market_value,
                        'pct_chg_5d': pct_chg_5d,
                        'pct_chg_20d': pct_chg_20d,
                        'pe_ratio': random.uniform(10, 50),
                        'pb_ratio': random.uniform(1, 5),
                        'roe': random.uniform(5, 20)
                    }

                    all_stocks.append(stock_data)
                    print(f"  ✅ {stock_name} ({stock_code}) - 评分: {score:.1f}, 市值: {market_value}亿")

                except Exception as e:
                    print(f"  ❌ 分析股票 {stock_code} 失败: {e}")
                    continue
                    
        except Exception as e:
            print(f"  ❌ 获取板块 {concept['name']} 失败: {e}")
            continue
    
    if not all_stocks:
        print("\n❌ 未找到任何股票数据")
        return
    
    # 转换为DataFrame并排序
    df = pd.DataFrame(all_stocks)
    df = df.sort_values('score', ascending=False)
    
    # 显示结果
    print(f"\n🎯 筛选出 {len(df)} 只热点板块龙头股:")
    print("=" * 120)
    print(f"{'排名':<4} {'代码':<12} {'名称':<12} {'板块':<15} {'行业':<12} {'评分':<6} {'市值(亿)':<10} {'5日涨幅%':<10} {'20日涨幅%':<10}")
    print("=" * 120)
    
    for i, (_, stock) in enumerate(df.head(20).iterrows(), 1):
        print(f"{i:<4} {stock['ts_code']:<12} {stock['name']:<12} {stock['sector']:<15} "
              f"{stock['industry']:<12} {stock['score']:<6.1f} {stock['market_value']:<10.0f} "
              f"{stock['pct_chg_5d']:<10.2f} {stock['pct_chg_20d']:<10.2f}")
    
    # 保存结果
    output_file = f"hot_leader_stocks_{datetime.now().strftime('%Y%m%d_%H%M')}.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n💾 结果已保存到: {output_file}")
    
    # 统计信息
    print(f"\n📊 统计信息:")
    print(f"  总计龙头股数量: {len(df)}")
    print(f"  平均评分: {df['score'].mean():.2f}")
    print(f"  平均市值: {df['market_value'].mean():.0f}亿")
    print(f"  涉及热点板块: {df['sector'].nunique()}个")
    
    print(f"\n🏭 板块分布:")
    for sector, count in df['sector'].value_counts().items():
        avg_score = df[df['sector'] == sector]['score'].mean()
        print(f"  {sector}: {count}只 (平均评分: {avg_score:.1f})")
    
    print(f"\n💡 投资建议:")
    top_stocks = df.head(5)
    print("  推荐关注以下龙头股:")
    for _, stock in top_stocks.iterrows():
        print(f"  🌟 {stock['name']} ({stock['ts_code']}) - {stock['sector']} - 评分: {stock['score']:.1f}")

if __name__ == "__main__":
    get_simple_hot_stocks()
