import pandas as pd
from datetime import datetime

def generate_stock_analysis_report(csv_file):
    """生成股票池分析报告"""
    
    # 读取数据
    df = pd.read_csv(csv_file, encoding='utf-8-sig')
    
    print("=" * 80)
    print("热点板块优质股票池分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"数据来源: {csv_file}")
    print()
    
    # 基本统计信息
    print("📊 基本统计信息")
    print("-" * 40)
    print(f"总股票数量: {len(df)} 只")
    print(f"涉及板块数量: {df['sector'].nunique()} 个")
    print(f"平均评分: {df['score'].mean():.2f} 分")
    print(f"评分范围: {df['score'].min():.1f} - {df['score'].max():.1f} 分")
    print()
    
    # 板块分布
    print("🏭 板块分布")
    print("-" * 40)
    sector_counts = df['sector'].value_counts()
    for sector, count in sector_counts.items():
        percentage = (count / len(df)) * 100
        print(f"{sector}: {count}只 ({percentage:.1f}%)")
    print()
    
    # 评分分布
    print("⭐ 评分分布")
    print("-" * 40)
    score_ranges = [
        (70, 100, "优秀"),
        (60, 70, "良好"), 
        (50, 60, "中等"),
        (40, 50, "一般"),
        (0, 40, "较差")
    ]
    
    for min_score, max_score, level in score_ranges:
        count = len(df[(df['score'] >= min_score) & (df['score'] < max_score)])
        if min_score == 70:  # 最高档包含等于70的情况
            count = len(df[df['score'] >= min_score])
        percentage = (count / len(df)) * 100 if len(df) > 0 else 0
        print(f"{level} ({min_score}-{max_score}分): {count}只 ({percentage:.1f}%)")
    print()
    
    # 财务指标统计
    print("💰 财务指标统计")
    print("-" * 40)
    financial_stats = {
        'PE市盈率': df['pe_ratio'],
        'PB市净率': df['pb_ratio'], 
        'ROE净资产收益率(%)': df['roe'],
        '流动比率': df['current_ratio']
    }
    
    for indicator, values in financial_stats.items():
        print(f"{indicator}:")
        print(f"  平均值: {values.mean():.2f}")
        print(f"  中位数: {values.median():.2f}")
        print(f"  范围: {values.min():.2f} - {values.max():.2f}")
        print()
    
    # 技术指标统计
    print("📈 技术指标统计")
    print("-" * 40)
    technical_stats = {
        '5日涨跌幅(%)': df['pct_chg_5d'],
        '20日涨跌幅(%)': df['pct_chg_20d'],
        '成交量比率': df['volume_ratio'],
        'RSI指标': df['rsi']
    }
    
    for indicator, values in technical_stats.items():
        print(f"{indicator}:")
        print(f"  平均值: {values.mean():.2f}")
        print(f"  中位数: {values.median():.2f}")
        print(f"  范围: {values.min():.2f} - {values.max():.2f}")
        print()
    
    # 推荐股票 (评分前10)
    print("🌟 推荐股票 (评分前10)")
    print("-" * 80)
    top_stocks = df.nlargest(10, 'score')
    
    print(f"{'排名':<4} {'代码':<12} {'名称':<12} {'板块':<12} {'评分':<6} {'PE':<8} {'PB':<8} {'ROE':<8} {'5日涨幅%':<10}")
    print("-" * 80)
    
    for idx, (_, stock) in enumerate(top_stocks.iterrows(), 1):
        print(f"{idx:<4} {stock['ts_code']:<12} {stock['name']:<12} {stock['sector']:<12} "
              f"{stock['score']:<6.1f} {stock['pe_ratio']:<8.2f} {stock['pb_ratio']:<8.2f} "
              f"{stock['roe']:<8.2f} {stock['pct_chg_5d']:<10.2f}")
    print()
    
    # 风险提示
    print("⚠️  风险提示")
    print("-" * 40)
    
    # 高PE股票
    high_pe_stocks = df[df['pe_ratio'] > 50]
    if len(high_pe_stocks) > 0:
        print(f"• 高PE风险: {len(high_pe_stocks)}只股票PE超过50倍，估值偏高")
        for _, stock in high_pe_stocks.iterrows():
            print(f"  - {stock['name']} ({stock['ts_code']}): PE {stock['pe_ratio']:.1f}")
    
    # 负ROE股票
    negative_roe_stocks = df[df['roe'] < 0]
    if len(negative_roe_stocks) > 0:
        print(f"• 盈利风险: {len(negative_roe_stocks)}只股票ROE为负，盈利能力较差")
        for _, stock in negative_roe_stocks.iterrows():
            print(f"  - {stock['name']} ({stock['ts_code']}): ROE {stock['roe']:.2f}%")
    
    # ST股票
    st_stocks = df[df['name'].str.contains('ST', na=False)]
    if len(st_stocks) > 0:
        print(f"• 特别处理风险: {len(st_stocks)}只ST股票，存在退市风险")
        for _, stock in st_stocks.iterrows():
            print(f"  - {stock['name']} ({stock['ts_code']})")
    
    print()
    print("=" * 80)
    print("注: 本报告仅供参考，投资有风险，入市需谨慎！")
    print("=" * 80)

if __name__ == "__main__":
    # 生成分析报告
    csv_file = f"hot_sector_stocks_{datetime.now().strftime('%Y%m%d')}.csv"
    generate_stock_analysis_report(csv_file)
