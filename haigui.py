import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 1. 设置Tushare Token (从环境变量读取，更安全)
your_tushare_token = os.getenv('TUSHARE_TOKEN', '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
ts.set_token(your_tushare_token)
pro = ts.pro_api()

class TurtleStrategy:
    """完整的海龟策略实现"""

    def __init__(self, initial_capital=30000, risk_per_trade=0.01, max_units=4,
                 commission_rate=0.0003, slippage=0.001):
        """
        初始化海龟策略参数

        Args:
            initial_capital: 初始资金
            risk_per_trade: 每笔交易风险比例 (1% = 0.01)
            max_units: 最大持仓单位数
            commission_rate: 手续费率
            slippage: 滑点
        """
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_units = max_units
        self.commission_rate = commission_rate
        self.slippage = slippage

        # 策略参数
        self.sys1_entry = 20  # 系统1入场突破天数
        self.sys1_exit = 10   # 系统1出场突破天数
        self.sys2_entry = 55  # 系统2入场突破天数
        self.sys2_exit = 20   # 系统2出场突破天数
        self.atr_period = 20  # ATR计算周期

        # 回测状态变量
        self.reset_state()

    def reset_state(self):
        """重置回测状态"""
        self.cash = self.initial_capital
        self.position = 0
        self.units = 0
        self.entry_prices = []  # 记录每个单位的入场价格
        self.last_entry_price = 0
        self.stop_loss_price = 0
        self.trade_log = []
        self.equity_curve = []
        self.current_system = None  # 当前使用的系统 (1 或 2)

    def calculate_indicators(self, df):
        """计算技术指标"""
        # 计算真实波动幅度（TR）和N值（ATR）
        df['prev_close'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
        df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
        df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
        df['N'] = df['TR'].rolling(window=self.atr_period).mean()

        # 系统1信号（避免前视偏差）
        df['sys1_entry_signal'] = df['high'].rolling(window=self.sys1_entry).max().shift(1)
        df['sys1_exit_signal'] = df['low'].rolling(window=self.sys1_exit).min().shift(1)

        # 系统2信号
        df['sys2_entry_signal'] = df['high'].rolling(window=self.sys2_entry).max().shift(1)
        df['sys2_exit_signal'] = df['low'].rolling(window=self.sys2_exit).min().shift(1)

        # 绘图用指标（不位移）
        df['sys1_entry_plot'] = df['high'].rolling(window=self.sys1_entry).max()
        df['sys1_exit_plot'] = df['low'].rolling(window=self.sys1_exit).min()
        df['sys2_entry_plot'] = df['high'].rolling(window=self.sys2_entry).max()
        df['sys2_exit_plot'] = df['low'].rolling(window=self.sys2_exit).min()

        return df

    def calculate_position_size(self, current_n, current_price):
        """计算仓位大小"""
        if current_n <= 0:
            return 0

        # 计算单位价值：让1N的波动等于账户的风险比例
        current_equity = self.cash + self.position * current_price
        risk_amount = current_equity * self.risk_per_trade
        unit_value = risk_amount / current_n

        # 转换为股数（A股按100股为单位）
        shares = int(unit_value / current_price / 100) * 100
        return max(shares, 0)

    def should_add_position(self, current_high):
        """判断是否应该加仓"""
        if self.units >= self.max_units or self.units == 0:
            return False

        # 计算下一个加仓点
        next_add_price = self.last_entry_price + 0.5 * self.current_n

        # 当日最高价突破加仓点时加仓
        if current_high >= next_add_price:
            return True, next_add_price
        return False, 0

    def calculate_stop_loss(self, current_n):
        """计算止损价格"""
        if self.units == 0:
            return 0

        # 海龟策略：2N止损
        avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
        stop_price = avg_entry_price - 2 * current_n
        return stop_price

    def execute_trade(self, date, action, price, shares, reason=""):
        """执行交易并记录"""
        if action == 'BUY':
            cost = shares * price * (1 + self.commission_rate + self.slippage)
            if cost <= self.cash:
                self.cash -= cost
                self.position += shares
                self.units += 1
                self.entry_prices.append(price)
                self.last_entry_price = price

                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': shares, 'units': self.units, 'cash': self.cash,
                    'reason': reason
                })
                return True

        elif action == 'SELL':
            if self.position > 0:
                proceeds = self.position * price * (1 - self.commission_rate - self.slippage)
                self.cash += proceeds

                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': self.position, 'units': self.units, 'cash': self.cash,
                    'reason': reason
                })

                self.position = 0
                self.units = 0
                self.entry_prices = []
                self.last_entry_price = 0
                self.stop_loss_price = 0
                self.current_system = None
                return True

        return False

    def backtest(self, df):
        """执行回测"""
        print("开始海龟策略回测...")
        self.reset_state()

        # 计算技术指标
        df = self.calculate_indicators(df)

        for i, row in df.iterrows():
            # 跳过NaN值
            if pd.isna(row['N']) or pd.isna(row['sys1_entry_signal']) or pd.isna(row['sys1_exit_signal']):
                current_equity = self.cash + self.position * row['close']
                self.equity_curve.append(current_equity)
                continue

            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_n = row['N']
            self.current_n = current_n

            # 更新止损价格
            if self.position > 0:
                self.stop_loss_price = self.calculate_stop_loss(current_n)

            # 1. 检查止损
            if self.position > 0 and current_low <= self.stop_loss_price:
                self.execute_trade(i, 'SELL', self.stop_loss_price, 0, "止损")

            # 2. 检查加仓信号（在出场信号之前检查）
            elif self.position > 0:
                should_add, add_price = self.should_add_position(current_high)
                if should_add:
                    shares = self.calculate_position_size(current_n, add_price)
                    if shares > 0:
                        success = self.execute_trade(i, 'BUY', add_price, shares, "加仓")
                        if success:
                            print(f"加仓成功: {i.strftime('%Y-%m-%d')}, 价格: {add_price:.2f}, 单位: {self.units}")

                # 3. 检查出场信号
                else:
                    exit_signal = False
                    if self.current_system == 1 and current_low <= row['sys1_exit_signal']:
                        exit_signal = True
                        exit_price = row['sys1_exit_signal']
                    elif self.current_system == 2 and current_low <= row['sys2_exit_signal']:
                        exit_signal = True
                        exit_price = row['sys2_exit_signal']

                    if exit_signal:
                        self.execute_trade(i, 'SELL', exit_price, 0, f"系统{self.current_system}出场")

            # 4. 检查入场信号
            elif self.position == 0:
                entry_signal = False
                entry_price = 0
                system = 0

                # 系统1入场信号
                if current_high > row['sys1_entry_signal']:
                    entry_signal = True
                    entry_price = row['sys1_entry_signal']
                    system = 1
                # 系统2入场信号（作为备选）
                elif current_high > row['sys2_entry_signal']:
                    entry_signal = True
                    entry_price = row['sys2_entry_signal']
                    system = 2

                if entry_signal:
                    shares = self.calculate_position_size(current_n, entry_price)
                    if shares > 0:
                        self.current_system = system
                        self.execute_trade(i, 'BUY', entry_price, shares, f"系统{system}入场")

            # 记录每日净资产
            current_equity = self.cash + self.position * current_close
            self.equity_curve.append(current_equity)

        return df

    def calculate_performance_metrics(self):
        """计算策略性能指标"""
        if len(self.equity_curve) < 2:
            return {}

        equity_series = pd.Series(self.equity_curve)
        returns = equity_series.pct_change().dropna()

        # 基本指标
        total_return = (self.equity_curve[-1] - self.initial_capital) / self.initial_capital

        # 最大回撤
        peak = equity_series.cummax()
        drawdown = (peak - equity_series) / peak
        max_drawdown = drawdown.max()

        # 年化收益率和波动率
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)

        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0

        # 胜率
        winning_trades = [t for t in self.trade_log if t['action'] == 'SELL' and 'reason' in t]
        total_trades = len([t for t in self.trade_log if t['action'] == 'BUY'])

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': total_trades,
            'win_rate': len(winning_trades) / total_trades if total_trades > 0 else 0
        }

    def print_results(self):
        """打印回测结果"""
        if not self.equity_curve:
            print("错误: 没有回测数据")
            return

        final_equity = self.equity_curve[-1]
        total_return = (final_equity - self.initial_capital) / self.initial_capital * 100

        print(f"\n{'='*50}")
        print(f"海龟策略回测结果")
        print(f"{'='*50}")
        print(f"初始资金: {self.initial_capital:,.2f} 元")
        print(f"最终资产: {final_equity:,.2f} 元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len([t for t in self.trade_log if t['action'] == 'BUY'])}")

        # 性能指标
        metrics = self.calculate_performance_metrics()
        if metrics:
            print(f"\n策略性能指标:")
            print(f"年化收益率: {metrics['annual_return']*100:.2f}%")
            print(f"年化波动率: {metrics['annual_volatility']*100:.2f}%")
            print(f"最大回撤: {metrics['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"胜率: {metrics['win_rate']*100:.2f}%")

        # 交易记录
        if self.trade_log:
            print(f"\n交易记录:")
            for trade in self.trade_log[-10:]:  # 只显示最后10笔交易
                print(f"日期: {trade['date'].strftime('%Y-%m-%d')}, "
                      f"操作: {trade['action']}, 价格: {trade['price']:.2f}, "
                      f"股数: {trade.get('shares', 0)}, 单位: {trade['units']}, "
                      f"原因: {trade.get('reason', '')}")
        else:
            print("没有发生任何交易")


def get_data():
    """获取历史数据"""
    try:
        df = pro.fund_daily(ts_code='510330.SH', start_date='20240701', end_date='20250827')
        print(f"成功获取数据，共{len(df)}行")

        if df.empty:
            print("获取的数据为空，请检查股票代码和日期范围")
            return None

        df = df.sort_values('trade_date')
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        print(f"日期范围: {df.index.min()} 到 {df.index.max()}")

        return df

    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None


def main():
    """主函数"""
    # 获取数据
    df = get_data()
    if df is None:
        return

    # 创建海龟策略实例
    turtle = TurtleStrategy(
        initial_capital=30000,
        risk_per_trade=0.01,  # 1%风险
        max_units=4,
        commission_rate=0.0003,  # 万3手续费
        slippage=0.001  # 0.1%滑点
    )

    # 执行回测
    df = turtle.backtest(df)

    # 打印结果
    turtle.print_results()

    # 绘制图表
    plot_results(df, turtle)


def plot_results(df, turtle):
    """绘制回测结果图表"""
    # 添加资金曲线到DataFrame
    df = df.iloc[:len(turtle.equity_curve)].copy()
    df['equity'] = turtle.equity_curve

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # 资金曲线
    ax1.plot(df.index, df['equity'], label='资金曲线', linewidth=2, color='blue')
    ax1.axhline(y=turtle.initial_capital, color='gray', linestyle='--', alpha=0.7, label='初始资金')
    ax1.set_title('海龟交易策略回测 - 资金曲线', fontsize=14)
    ax1.set_ylabel('资产价值 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 股价和交易信号
    ax2.plot(df.index, df['close'], label='股价', linewidth=1.5, color='black', alpha=0.8)
    ax2.plot(df.index, df['sys1_entry_plot'], label='系统1入场(20日高点)',
             linestyle='--', linewidth=1, color='red', alpha=0.7)
    ax2.plot(df.index, df['sys1_exit_plot'], label='系统1出场(10日低点)',
             linestyle='--', linewidth=1, color='green', alpha=0.7)
    ax2.plot(df.index, df['sys2_entry_plot'], label='系统2入场(55日高点)',
             linestyle=':', linewidth=1, color='orange', alpha=0.7)

    # 标记买入卖出点
    buy_trades = [t for t in turtle.trade_log if t['action'] == 'BUY']
    sell_trades = [t for t in turtle.trade_log if t['action'] == 'SELL']

    if buy_trades:
        buy_dates = [t['date'] for t in buy_trades]
        buy_prices = [t['price'] for t in buy_trades]
        buy_reasons = [t.get('reason', '') for t in buy_trades]

        # 区分首次入场和加仓
        first_entry = [i for i, r in enumerate(buy_reasons) if '入场' in r]
        add_position = [i for i, r in enumerate(buy_reasons) if '加仓' in r]

        if first_entry:
            ax2.scatter([buy_dates[i] for i in first_entry],
                       [buy_prices[i] for i in first_entry],
                       color='green', marker='^', s=120, label='首次入场', zorder=5)
        if add_position:
            ax2.scatter([buy_dates[i] for i in add_position],
                       [buy_prices[i] for i in add_position],
                       color='lightgreen', marker='^', s=80, label='加仓', zorder=5)

    if sell_trades:
        sell_dates = [t['date'] for t in sell_trades]
        sell_prices = [t['price'] for t in sell_trades]
        sell_reasons = [t.get('reason', '') for t in sell_trades]

        # 区分不同出场原因
        stop_loss = [i for i, r in enumerate(sell_reasons) if '止损' in r]
        normal_exit = [i for i, r in enumerate(sell_reasons) if '出场' in r]

        if stop_loss:
            ax2.scatter([sell_dates[i] for i in stop_loss],
                       [sell_prices[i] for i in stop_loss],
                       color='red', marker='v', s=120, label='止损', zorder=5)
        if normal_exit:
            ax2.scatter([sell_dates[i] for i in normal_exit],
                       [sell_prices[i] for i in normal_exit],
                       color='orange', marker='v', s=100, label='正常出场', zorder=5)

    ax2.set_title('股价与交易信号', fontsize=14)
    ax2.set_xlabel('日期')
    ax2.set_ylabel('价格 (元)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    main()