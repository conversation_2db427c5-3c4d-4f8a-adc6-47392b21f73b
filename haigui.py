import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 1. 设置Tushare Token (从环境变量读取，更安全)
your_tushare_token = os.getenv('TUSHARE_TOKEN', '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
ts.set_token(your_tushare_token)
pro = ts.pro_api()

class BalancedTurtleStrategy:
    """平衡版海龟策略 - 在胜率、频率、收益间找平衡"""

    def __init__(self, initial_capital=30000, risk_per_trade=0.01, max_units=4,
                 commission_rate=0.0003, slippage=0.001):
        """
        初始化平衡版海龟策略参数
        """
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_units = max_units
        self.commission_rate = commission_rate
        self.slippage = slippage

        # 策略参数
        self.sys1_entry = 20  # 系统1入场突破天数
        self.sys1_exit = 10   # 系统1出场突破天数
        self.sys2_entry = 55  # 系统2入场突破天数
        self.sys2_exit = 20   # 系统2出场突破天数
        self.atr_period = 20  # ATR计算周期

        # 止盈减仓参数
        self.profit_taking_levels = [10, 20]  # 10N和20N止盈减仓
        self.min_units_to_hold = 1  # 最少保留单位数

        # 平衡优化过滤参数
        self.enable_filters = True
        self.min_breakout_strength = 0.15     # 突破强度（平衡设置）
        self.trend_ma_period = 30              # 趋势均线（中期）
        self.min_volume_ratio = 1.2           # 成交量要求（适中）
        self.enable_pullback_entry = False    # 关闭强制回调入场

        # 回测状态变量
        self.reset_state()

    def reset_state(self):
        """重置回测状态"""
        self.cash = self.initial_capital
        self.position = 0
        self.units = 0
        self.entry_prices = []
        self.entry_dates = []
        self.unit_shares = []
        self.last_entry_price = 0
        self.stop_loss_price = 0
        self.trade_log = []
        self.equity_curve = []
        self.current_system = None
        self.profit_taking_executed = []

    def calculate_indicators(self, df):
        """计算技术指标"""
        # 计算真实波动幅度（TR）和N值（ATR）
        df['prev_close'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
        df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
        df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
        df['N'] = df['TR'].rolling(window=self.atr_period).mean()

        # 系统1信号（避免前视偏差）
        df['sys1_entry_signal'] = df['high'].rolling(window=self.sys1_entry).max().shift(1)
        df['sys1_exit_signal'] = df['low'].rolling(window=self.sys1_exit).min().shift(1)

        # 系统2信号
        df['sys2_entry_signal'] = df['high'].rolling(window=self.sys2_entry).max().shift(1)
        df['sys2_exit_signal'] = df['low'].rolling(window=self.sys2_exit).min().shift(1)

        # 绘图用指标（不位移）
        df['sys1_entry_plot'] = df['high'].rolling(window=self.sys1_entry).max()
        df['sys1_exit_plot'] = df['low'].rolling(window=self.sys1_exit).min()
        df['sys2_entry_plot'] = df['high'].rolling(window=self.sys2_entry).max()
        df['sys2_exit_plot'] = df['low'].rolling(window=self.sys2_exit).min()

        # 计算平衡过滤指标
        if self.enable_filters:
            df = self.calculate_filter_indicators(df)

        return df

    def calculate_filter_indicators(self, df):
        """计算平衡过滤指标"""
        # 1. 趋势指标
        df['ma_trend'] = df['close'].rolling(window=self.trend_ma_period).mean()
        df['above_trend'] = (df['close'] > df['ma_trend']).astype(int)

        # 2. 突破强度指标
        df['breakout_strength_sys1'] = (df['high'] - df['sys1_entry_signal']) / df['N']
        df['breakout_strength_sys2'] = (df['high'] - df['sys2_entry_signal']) / df['N']

        # 3. 成交量指标（模拟）
        np.random.seed(42)
        base_vol = 1000000
        df['vol'] = base_vol * (1 + 0.3 * np.random.randn(len(df)))
        df['vol'] = df['vol'].abs()
        df['vol_ma20'] = df['vol'].rolling(window=20).mean()
        df['vol_ratio'] = df['vol'] / df['vol_ma20']

        return df

    def check_entry_filters(self, row, system):
        """检查入场过滤条件（平衡版）"""
        if not self.enable_filters:
            return True, "无过滤器"

        filter_results = []

        # 1. 趋势过滤
        above_trend = row.get('above_trend', 1)
        if above_trend == 0:
            return False, f"价格低于{self.trend_ma_period}日均线"
        filter_results.append(f"趋势✓")

        # 2. 突破强度过滤
        if system == 1:
            breakout_strength = row.get('breakout_strength_sys1', 0)
        else:
            breakout_strength = row.get('breakout_strength_sys2', 0)

        if pd.notna(breakout_strength) and breakout_strength < self.min_breakout_strength:
            return False, f"突破强度不足({breakout_strength:.2f}N<{self.min_breakout_strength}N)"
        filter_results.append(f"突破强度✓({breakout_strength:.2f}N)")

        # 3. 成交量过滤（适中要求）
        vol_ratio = row.get('vol_ratio', 1.0)
        if vol_ratio < self.min_volume_ratio:
            return False, f"成交量不足({vol_ratio:.1f}<{self.min_volume_ratio})"
        filter_results.append(f"成交量✓({vol_ratio:.1f})")

        return True, " | ".join(filter_results)

    def calculate_position_size(self, current_n, current_price):
        """计算仓位大小"""
        if current_n <= 0:
            return 0

        current_equity = self.cash + self.position * current_price
        risk_amount = current_equity * self.risk_per_trade
        unit_value = risk_amount / current_n
        shares = int(unit_value / current_price / 100) * 100
        return max(shares, 0)

    def should_add_position(self, current_high):
        """判断是否应该加仓"""
        if self.units >= self.max_units or self.units == 0:
            return False, 0

        next_add_price = self.last_entry_price + 0.5 * self.current_n
        if current_high >= next_add_price:
            return True, next_add_price
        return False, 0

    def calculate_stop_loss(self, current_n):
        """计算止损价格"""
        if self.units == 0:
            return 0
        avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
        stop_price = avg_entry_price - 2 * current_n
        return stop_price
    # 跳过NaN值（前期数据不足计算指标）
    if pd.isna(row['N']) or pd.isna(row['20d_high_signal']) or pd.isna(row['10d_low_signal']):
        equity_curve.append(cash + position * row['close'])
        continue
        
    current_close = row['close']
    current_high = row['high']
    current_low = row['low']

    current_n = row['N']
    atr = current_n
    
    # 信号判断（使用位移后的信号）
    buy_signal_sys1 = (current_high > row['20d_high_signal']) and (units < 4)
    sell_signal = (current_low < row['10d_low_signal']) and (position > 0)

    # 交易执行逻辑
    if sell_signal:
        # 平仓：卖出所有持仓
        cash += position * row['10d_low_signal']
        trade_log.append({'date': i, 'action': 'SELL', 'price': row['10d_low_signal'], 'units': units, 'cash': cash})
        position = 0
        units = 0
        entry_price = 0

    elif buy_signal_sys1 and cash > 0 and atr > 0:
        # 计算风险：目标是让1N的波动等于本金的1%
        risk_per_unit = atr
        unit_value = initial_cash * 0.01 / risk_per_unit # 1%本金风险对应的股数
        unit_value = int(unit_value / 100) * 100  # A股按手交易，简化按100股取整

        if unit_value > 0:
            # 计算本次可买入的股数（1个单位）
            shares_to_buy = unit_value
            # 确保资金足够
            cost = shares_to_buy * row['20d_high_signal']
            if cost <= cash:
                cash -= cost
                position += shares_to_buy
                units += 1
                entry_price = row['20d_high_signal'] if entry_price == 0 else (entry_price * (units-1) + current_close) / units # 计算平均入场价
                trade_log.append({'date': i, 'action': 'BUY', 'price': row['20d_high_signal'], 'units': units, 'cash': cash})

    # 记录每日净资产
    daily_equity = cash + position * current_close
    equity_curve.append(daily_equity)

# 6. 检查equity_curve是否有数据
if not equity_curve:
    print("错误: equity_curve为空，没有进行任何回测计算")
    exit()

# 7. 将回测结果添加到DataFrame
df = df.iloc[:len(equity_curve)]  # 确保df与equity_curve长度一致
df['equity'] = equity_curve

# 8. 输出最终结果和交易记录
final_equity = equity_curve[-1]
total_return = (final_equity - initial_cash) / initial_cash * 100
print(f"\n初始资金: {initial_cash:.2f} 元")
print(f"最终资产: {final_equity:.2f} 元")
print(f"总收益率: {total_return:.2f}%")
print(f"交易次数: {len(trade_log)}")

if trade_log:
    print("\n交易记录:")
    for trade in trade_log:
        print(f"日期: {trade['date'].strftime('%Y-%m-%d')}, 操作: {trade['action']}, "
              f"价格: {trade['price']:.2f}, 单位: {trade['units']}, 现金: {trade['cash']:.2f}")
else:
    print("没有发生任何交易")

# 9. 绘制资金曲线和股价对比
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))

# 资金曲线
ax1.plot(df.index, df['equity'], label='资金曲线', linewidth=1.5, color='blue')
ax1.set_title('海龟交易策略回测 - 资金曲线')
ax1.set_ylabel('资产价值 (元)')
ax1.legend()
ax1.grid(True)

# 股价和交易信号
ax2.plot(df.index, df['close'], label='股价', linewidth=1.5, color='black', alpha=0.8)
ax2.plot(df.index, df['20d_high_plot'], label='20日高点', linestyle='--', linewidth=1.2, color='red', alpha=0.8)
ax2.plot(df.index, df['10d_low_plot'], label='10日低点', linestyle='--', linewidth=1.2, color='green', alpha=0.8)

# 标记买入卖出点
buy_dates = [trade['date'] for trade in trade_log if trade['action'] == 'BUY']
sell_dates = [trade['date'] for trade in trade_log if trade['action'] == 'SELL']
buy_prices = [trade['price'] for trade in trade_log if trade['action'] == 'BUY']
sell_prices = [trade['price'] for trade in trade_log if trade['action'] == 'SELL']

if buy_dates:
    ax2.scatter(buy_dates, buy_prices, color='green', marker='^', s=100, label='买入点', zorder=5)
if sell_dates:
    ax2.scatter(sell_dates, sell_prices, color='red', marker='v', s=100, label='卖出点', zorder=5)

ax2.set_title('股价与交易信号')
ax2.set_xlabel('日期')
ax2.set_ylabel('价格 (元)')
ax2.legend()
ax2.grid(True)

plt.tight_layout()
plt.show()

# 10. 输出策略性能指标
if len(equity_curve) > 1:
    returns = pd.Series(equity_curve).pct_change().dropna()
    max_drawdown = (pd.Series(equity_curve).cummax() - pd.Series(equity_curve)).max() / pd.Series(equity_curve).cummax().max()
    
    print(f"\n策略性能指标:")
    print(f"年化收益率: {returns.mean() * 252 * 100:.2f}%")  # 假设252个交易日
    print(f"最大回撤: {max_drawdown * 100:.2f}%")
    print(f"夏普比率: {returns.mean() / returns.std() * np.sqrt(252):.2f}")  # 简化计算