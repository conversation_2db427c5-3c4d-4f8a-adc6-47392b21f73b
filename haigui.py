import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
from datetime import datetime

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 1. 设置Tushare Token (从环境变量读取，更安全)
your_tushare_token = os.getenv('TUSHARE_TOKEN', '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
ts.set_token(your_tushare_token)
pro = ts.pro_api()

class TurtleStrategy:
    """完整的海龟策略实现"""

    def __init__(self, initial_capital=30000, risk_per_trade=0.01, max_units=4,
                 commission_rate=0.0003, slippage=0.001):
        """
        初始化海龟策略参数

        Args:
            initial_capital: 初始资金
            risk_per_trade: 每笔交易风险比例 (1% = 0.01)
            max_units: 最大持仓单位数
            commission_rate: 手续费率
            slippage: 滑点
        """
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_units = max_units
        self.commission_rate = commission_rate
        self.slippage = slippage

        # 策略参数
        self.sys1_entry = 20  # 系统1入场突破天数
        self.sys1_exit = 10   # 系统1出场突破天数
        self.sys2_entry = 55  # 系统2入场突破天数
        self.sys2_exit = 20   # 系统2出场突破天数
        self.atr_period = 20  # ATR计算周期

        # 回测状态变量
        self.reset_state()

    def reset_state(self):
        """重置回测状态"""
        self.cash = self.initial_capital
        self.position = 0
        self.units = 0
        self.entry_prices = []  # 记录每个单位的入场价格
        self.last_entry_price = 0
        self.stop_loss_price = 0
        self.trade_log = []
        self.equity_curve = []
        self.current_system = None  # 当前使用的系统 (1 或 2)

    def calculate_indicators(self, df):
        """计算技术指标"""
        # 计算真实波动幅度（TR）和N值（ATR）
        df['prev_close'] = df['close'].shift(1)
        df['high_low'] = df['high'] - df['low']
        df['high_prevclose'] = np.abs(df['high'] - df['prev_close'])
        df['low_prevclose'] = np.abs(df['low'] - df['prev_close'])
        df['TR'] = df[['high_low', 'high_prevclose', 'low_prevclose']].max(axis=1)
        df['N'] = df['TR'].rolling(window=self.atr_period).mean()

        # 系统1信号（避免前视偏差）
        df['sys1_entry_signal'] = df['high'].rolling(window=self.sys1_entry).max().shift(1)
        df['sys1_exit_signal'] = df['low'].rolling(window=self.sys1_exit).min().shift(1)

        # 系统2信号
        df['sys2_entry_signal'] = df['high'].rolling(window=self.sys2_entry).max().shift(1)
        df['sys2_exit_signal'] = df['low'].rolling(window=self.sys2_exit).min().shift(1)

        # 绘图用指标（不位移）
        df['sys1_entry_plot'] = df['high'].rolling(window=self.sys1_entry).max()
        df['sys1_exit_plot'] = df['low'].rolling(window=self.sys1_exit).min()
        df['sys2_entry_plot'] = df['high'].rolling(window=self.sys2_entry).max()
        df['sys2_exit_plot'] = df['low'].rolling(window=self.sys2_exit).min()

        return df

    def calculate_position_size(self, current_n, current_price):
        """计算仓位大小"""
        if current_n <= 0:
            return 0

        # 计算单位价值：让1N的波动等于账户的风险比例
        current_equity = self.cash + self.position * current_price
        risk_amount = current_equity * self.risk_per_trade
        unit_value = risk_amount / current_n

        # 转换为股数（A股按100股为单位）
        shares = int(unit_value / current_price / 100) * 100
        return max(shares, 0)

    def should_add_position(self, current_high):
        """判断是否应该加仓"""
        if self.units >= self.max_units or self.units == 0:
            return False, 0

        # 计算下一个加仓点
        next_add_price = self.last_entry_price + 0.5 * self.current_n

        # 当日最高价突破加仓点时加仓
        if current_high >= next_add_price:
            return True, next_add_price
        return False, 0

    def calculate_stop_loss(self, current_n):
        """计算止损价格"""
        if self.units == 0:
            return 0

        # 海龟策略：2N止损
        avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
        stop_price = avg_entry_price - 2 * current_n
        return stop_price

    def analyze_trade_decision(self, date, action, price, shares, reason, current_n):
        """分析交易决策，生成详细的交易计划"""
        if action != 'BUY':
            return {}

        # 计算风险和收益预期
        current_equity = self.cash + self.position * price
        position_risk = shares * price / current_equity
        unit_risk = current_n / price  # 单位风险（1N相对价格的比例）

        # 计算止损价格
        if self.units == 0:  # 首次入场
            stop_loss = price - 2 * current_n
        else:  # 加仓
            avg_entry = np.mean(self.entry_prices) if self.entry_prices else price
            stop_loss = avg_entry - 2 * current_n

        # 计算下一个加仓点
        next_add_price = price + 0.5 * current_n if self.units < self.max_units else None

        # 分析市场状态
        market_analysis = self.analyze_market_state(price, current_n)

        return {
            'position_risk': position_risk,
            'unit_risk': unit_risk,
            'stop_loss': stop_loss,
            'next_add_price': next_add_price,
            'market_analysis': market_analysis,
            'risk_reward_ratio': (next_add_price - price) / (price - stop_loss) if next_add_price else None
        }

    def analyze_market_state(self, price, current_n):
        """分析当前市场状态"""
        volatility_level = "高" if current_n / price > 0.03 else "中" if current_n / price > 0.015 else "低"

        return {
            'volatility': volatility_level,
            'atr_ratio': current_n / price,
            'trend_strength': "强" if current_n / price < 0.02 else "弱"
        }

    def print_trade_decision(self, date, action, price, shares, reason, analysis, current_n):
        """打印详细的交易决策分析"""
        if action != 'BUY':
            return

        print(f"\n{'='*80}")
        print(f"🔥 交易决策分析 - {date.strftime('%Y-%m-%d')}")
        print(f"{'='*80}")

        # 基本信息
        print(f"📊 交易信息:")
        print(f"   操作类型: {action} ({reason})")
        print(f"   交易价格: {price:.2f} 元")
        print(f"   交易股数: {shares:,} 股")
        print(f"   交易金额: {shares * price:,.2f} 元")
        print(f"   当前单位: {self.units} / {self.max_units}")

        # 风险分析
        print(f"\n🎯 风险控制:")
        print(f"   单笔仓位占比: {analysis['position_risk']*100:.1f}%")
        print(f"   单位风险(1N): {analysis['unit_risk']*100:.2f}%")
        print(f"   止损价格: {analysis['stop_loss']:.2f} 元")
        print(f"   止损幅度: {(price - analysis['stop_loss'])/price*100:.1f}%")

        # 市场分析
        market = analysis['market_analysis']
        print(f"\n📈 市场状态:")
        print(f"   波动率水平: {market['volatility']} (ATR/价格: {market['atr_ratio']*100:.2f}%)")
        print(f"   趋势强度: {market['trend_strength']}")
        print(f"   当前N值: {current_n:.3f}")

        # 交易理由和预期
        self.print_trade_rationale(reason, price, analysis)

        # 应对计划
        self.print_contingency_plan(reason, price, analysis, current_n)

    def print_trade_rationale(self, reason, price, analysis):
        """打印交易理由和预期"""
        print(f"\n💡 交易理由和预期:")

        if "系统1入场" in reason:
            print(f"   ✅ 系统1入场信号 (20日突破)")
            print(f"   📋 理由: 价格突破20日最高点，表明短期趋势启动")
            print(f"   🎯 预期: 趋势延续，价格继续上涨")
            print(f"   📊 概率: 海龟策略历史胜率约40-50%")

        elif "系统2入场" in reason:
            print(f"   ✅ 系统2入场信号 (55日突破)")
            print(f"   📋 理由: 价格突破55日最高点，表明长期趋势确立")
            print(f"   🎯 预期: 强势趋势，更大的获利空间")
            print(f"   📊 概率: 长期突破信号更可靠，但频率较低")

        elif "加仓" in reason:
            print(f"   ✅ 加仓信号 (0.5N突破)")
            print(f"   📋 理由: 价格继续上涨0.5N，验证趋势强度")
            print(f"   🎯 预期: 趋势加速，通过加仓放大收益")
            print(f"   📊 策略: 金字塔式加仓，让利润奔跑")

            if analysis['next_add_price']:
                print(f"   🔄 下次加仓点: {analysis['next_add_price']:.2f} 元")

        # 收益预期
        if analysis['risk_reward_ratio']:
            print(f"   💰 风险收益比: 1:{analysis['risk_reward_ratio']:.1f}")

    def print_contingency_plan(self, reason, price, analysis, current_n):
        """打印应对变数的计划"""
        print(f"\n🛡️ 风险应对计划:")

        print(f"   📉 如果价格下跌:")
        print(f"      • 止损线: {analysis['stop_loss']:.2f} 元 (严格执行)")
        print(f"      • 最大损失: {(price - analysis['stop_loss']) * self.position / self.initial_capital * 100:.1f}% 账户资金")
        print(f"      • 止损理由: 2N止损是海龟策略核心，控制单笔损失")

        print(f"   📈 如果价格上涨:")
        if analysis['next_add_price'] and self.units < self.max_units:
            print(f"      • 加仓点: {analysis['next_add_price']:.2f} 元")
            print(f"      • 加仓逻辑: 每上涨0.5N加仓一次，最多{self.max_units}个单位")
        else:
            print(f"      • 已达最大仓位，不再加仓")

        print(f"   🔄 出场计划:")
        if "系统1" in reason:
            print(f"      • 正常出场: 跌破10日最低点")
            print(f"      • 出场理由: 短期趋势反转信号")
        elif "系统2" in reason:
            print(f"      • 正常出场: 跌破20日最低点")
            print(f"      • 出场理由: 长期趋势反转信号")

        print(f"   ⚠️ 特殊情况:")
        print(f"      • 连续跳空下跌: 立即止损，不等触及止损线")
        print(f"      • 基本面恶化: 考虑提前出场")
        print(f"      • 系统性风险: 降低整体仓位")

    def execute_trade(self, date, action, price, shares, reason=""):
        """执行交易并记录"""
        if action == 'BUY':
            cost = shares * price * (1 + self.commission_rate + self.slippage)
            if cost <= self.cash:
                # 分析交易决策
                analysis = self.analyze_trade_decision(date, action, price, shares, reason, self.current_n)

                # 打印详细分析
                self.print_trade_decision(date, action, price, shares, reason, analysis, self.current_n)

                # 执行交易
                self.cash -= cost
                self.position += shares
                self.units += 1
                self.entry_prices.append(price)
                self.last_entry_price = price

                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': shares, 'units': self.units, 'cash': self.cash,
                    'reason': reason, 'analysis': analysis
                })
                return True

        elif action == 'SELL':
            if self.position > 0:
                # 计算交易结果
                avg_entry_price = np.mean(self.entry_prices) if self.entry_prices else 0
                total_return = (price - avg_entry_price) / avg_entry_price if avg_entry_price > 0 else 0
                profit_loss = self.position * (price - avg_entry_price)

                # 打印卖出分析
                self.print_sell_analysis(date, price, reason, avg_entry_price, total_return, profit_loss)

                proceeds = self.position * price * (1 - self.commission_rate - self.slippage)
                self.cash += proceeds

                self.trade_log.append({
                    'date': date, 'action': action, 'price': price,
                    'shares': self.position, 'units': self.units, 'cash': self.cash,
                    'reason': reason, 'profit_loss': profit_loss, 'return_rate': total_return
                })

                self.position = 0
                self.units = 0
                self.entry_prices = []
                self.last_entry_price = 0
                self.stop_loss_price = 0
                self.current_system = None
                return True

        return False

    def print_sell_analysis(self, date, price, reason, avg_entry_price, total_return, profit_loss):
        """打印卖出交易分析"""
        print(f"\n{'='*80}")
        print(f"📤 平仓分析 - {date.strftime('%Y-%m-%d')}")
        print(f"{'='*80}")

        print(f"📊 交易结果:")
        print(f"   平仓价格: {price:.2f} 元")
        print(f"   平均成本: {avg_entry_price:.2f} 元")
        print(f"   收益率: {total_return*100:+.2f}%")
        print(f"   盈亏金额: {profit_loss:+,.2f} 元")
        print(f"   平仓原因: {reason}")

        print(f"\n🎯 平仓分析:")
        if "止损" in reason:
            print(f"   ⚠️ 止损出场 - 风险控制生效")
            print(f"   📋 说明: 价格跌破2N止损线，严格执行风险控制")
            print(f"   ✅ 评价: 正确的风险管理，保护资本")
            print(f"   📚 经验: 小损失是大利润的代价，止损是必要的")

        elif "出场" in reason:
            print(f"   📈 趋势反转出场")
            if "系统1" in reason:
                print(f"   📋 说明: 价格跌破10日最低点，短期趋势反转")
            else:
                print(f"   📋 说明: 价格跌破20日最低点，长期趋势反转")

            if total_return > 0:
                print(f"   ✅ 评价: 成功捕获趋势，及时获利了结")
                print(f"   📚 经验: 让利润奔跑，但要在趋势反转时果断出场")
            else:
                print(f"   ⚠️ 评价: 虽然亏损，但遵循了系统规则")
                print(f"   📚 经验: 不是每次突破都能成功，关键是执行纪律")

        # 交易总结
        if total_return > 0.1:  # 10%以上收益
            print(f"   🎉 优秀交易: 获得了显著收益")
        elif total_return > 0:
            print(f"   👍 盈利交易: 小幅获利")
        elif total_return > -0.02:  # 2%以内损失
            print(f"   😐 小幅亏损: 在可接受范围内")
        else:
            print(f"   😞 较大亏损: 需要反思和总结")

    def backtest(self, df):
        """执行回测"""
        print("开始海龟策略回测...")
        self.reset_state()

        # 计算技术指标
        df = self.calculate_indicators(df)

        for i, row in df.iterrows():
            # 跳过NaN值
            if pd.isna(row['N']) or pd.isna(row['sys1_entry_signal']) or pd.isna(row['sys1_exit_signal']):
                current_equity = self.cash + self.position * row['close']
                self.equity_curve.append(current_equity)
                continue

            current_close = row['close']
            current_high = row['high']
            current_low = row['low']
            current_n = row['N']
            self.current_n = current_n

            # 更新止损价格
            if self.position > 0:
                self.stop_loss_price = self.calculate_stop_loss(current_n)

            # 1. 检查止损
            if self.position > 0 and current_low <= self.stop_loss_price:
                self.execute_trade(i, 'SELL', self.stop_loss_price, 0, "止损")

            # 2. 检查加仓信号（在出场信号之前检查）
            elif self.position > 0:
                should_add, add_price = self.should_add_position(current_high)
                if should_add:
                    shares = self.calculate_position_size(current_n, add_price)
                    if shares > 0:
                        success = self.execute_trade(i, 'BUY', add_price, shares, "加仓")
                        if success:
                            print(f"加仓成功: {i.strftime('%Y-%m-%d')}, 价格: {add_price:.2f}, 单位: {self.units}")

                # 3. 检查出场信号
                else:
                    exit_signal = False
                    if self.current_system == 1 and current_low <= row['sys1_exit_signal']:
                        exit_signal = True
                        exit_price = row['sys1_exit_signal']
                    elif self.current_system == 2 and current_low <= row['sys2_exit_signal']:
                        exit_signal = True
                        exit_price = row['sys2_exit_signal']

                    if exit_signal:
                        self.execute_trade(i, 'SELL', exit_price, 0, f"系统{self.current_system}出场")

            # 4. 检查入场信号
            elif self.position == 0:
                entry_signal = False
                entry_price = 0
                system = 0

                # 系统1入场信号
                if current_high > row['sys1_entry_signal']:
                    entry_signal = True
                    entry_price = row['sys1_entry_signal']
                    system = 1
                # 系统2入场信号（作为备选）
                elif current_high > row['sys2_entry_signal']:
                    entry_signal = True
                    entry_price = row['sys2_entry_signal']
                    system = 2

                if entry_signal:
                    shares = self.calculate_position_size(current_n, entry_price)
                    if shares > 0:
                        self.current_system = system
                        self.execute_trade(i, 'BUY', entry_price, shares, f"系统{system}入场")

            # 记录每日净资产
            current_equity = self.cash + self.position * current_close
            self.equity_curve.append(current_equity)

        return df

    def calculate_performance_metrics(self):
        """计算策略性能指标"""
        if len(self.equity_curve) < 2:
            return {}

        equity_series = pd.Series(self.equity_curve)
        returns = equity_series.pct_change().dropna()

        # 基本指标
        total_return = (self.equity_curve[-1] - self.initial_capital) / self.initial_capital

        # 最大回撤
        peak = equity_series.cummax()
        drawdown = (peak - equity_series) / peak
        max_drawdown = drawdown.max()

        # 年化收益率和波动率
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)

        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0

        # 胜率
        winning_trades = [t for t in self.trade_log if t['action'] == 'SELL' and 'reason' in t]
        total_trades = len([t for t in self.trade_log if t['action'] == 'BUY'])

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': total_trades,
            'win_rate': len(winning_trades) / total_trades if total_trades > 0 else 0
        }

    def print_results(self):
        """打印回测结果"""
        if not self.equity_curve:
            print("错误: 没有回测数据")
            return

        final_equity = self.equity_curve[-1]
        total_return = (final_equity - self.initial_capital) / self.initial_capital * 100

        print(f"\n{'='*50}")
        print(f"海龟策略回测结果")
        print(f"{'='*50}")
        print(f"初始资金: {self.initial_capital:,.2f} 元")
        print(f"最终资产: {final_equity:,.2f} 元")
        print(f"总收益率: {total_return:.2f}%")
        print(f"交易次数: {len([t for t in self.trade_log if t['action'] == 'BUY'])}")

        # 性能指标
        metrics = self.calculate_performance_metrics()
        if metrics:
            print(f"\n策略性能指标:")
            print(f"年化收益率: {metrics['annual_return']*100:.2f}%")
            print(f"年化波动率: {metrics['annual_volatility']*100:.2f}%")
            print(f"最大回撤: {metrics['max_drawdown']*100:.2f}%")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"胜率: {metrics['win_rate']*100:.2f}%")

        # 交易记录
        if self.trade_log:
            print(f"\n交易记录:")
            for trade in self.trade_log[-10:]:  # 只显示最后10笔交易
                print(f"日期: {trade['date'].strftime('%Y-%m-%d')}, "
                      f"操作: {trade['action']}, 价格: {trade['price']:.2f}, "
                      f"股数: {trade.get('shares', 0)}, 单位: {trade['units']}, "
                      f"原因: {trade.get('reason', '')}")
        else:
            print("没有发生任何交易")


def get_data():
    """获取历史数据"""
    try:
        df = pro.fund_daily(ts_code='510330.SH', start_date='20240701', end_date='20250827')
        print(f"成功获取数据，共{len(df)}行")

        if df.empty:
            print("获取的数据为空，请检查股票代码和日期范围")
            return None

        df = df.sort_values('trade_date')
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        print(f"日期范围: {df.index.min()} 到 {df.index.max()}")

        return df

    except Exception as e:
        print(f"获取数据时出错: {e}")
        return None


def main():
    """主函数"""
    # 获取数据
    df = get_data()
    if df is None:
        return

    # 创建海龟策略实例
    turtle = TurtleStrategy(
        initial_capital=30000,
        risk_per_trade=0.01,  # 1%风险
        max_units=4,
        commission_rate=0.0003,  # 万3手续费
        slippage=0.001  # 0.1%滑点
    )

    # 执行回测
    df = turtle.backtest(df)

    # 打印结果
    turtle.print_results()

    # 绘制图表
    plot_results(df, turtle)


def plot_results(df, turtle):
    """绘制回测结果图表"""
    # 添加资金曲线到DataFrame
    df = df.iloc[:len(turtle.equity_curve)].copy()
    df['equity'] = turtle.equity_curve

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))

    # 资金曲线
    ax1.plot(df.index, df['equity'], label='资金曲线', linewidth=2, color='blue')
    ax1.axhline(y=turtle.initial_capital, color='gray', linestyle='--', alpha=0.7, label='初始资金')
    ax1.set_title('海龟交易策略回测 - 资金曲线', fontsize=14)
    ax1.set_ylabel('资产价值 (元)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 股价和交易信号
    ax2.plot(df.index, df['close'], label='股价', linewidth=1.5, color='black', alpha=0.8)
    ax2.plot(df.index, df['sys1_entry_plot'], label='系统1入场(20日高点)',
             linestyle='--', linewidth=1, color='red', alpha=0.7)
    ax2.plot(df.index, df['sys1_exit_plot'], label='系统1出场(10日低点)',
             linestyle='--', linewidth=1, color='green', alpha=0.7)
    ax2.plot(df.index, df['sys2_entry_plot'], label='系统2入场(55日高点)',
             linestyle=':', linewidth=1, color='orange', alpha=0.7)

    # 标记买入卖出点
    buy_trades = [t for t in turtle.trade_log if t['action'] == 'BUY']
    sell_trades = [t for t in turtle.trade_log if t['action'] == 'SELL']

    if buy_trades:
        buy_dates = [t['date'] for t in buy_trades]
        buy_prices = [t['price'] for t in buy_trades]
        buy_reasons = [t.get('reason', '') for t in buy_trades]

        # 区分首次入场和加仓
        first_entry = [i for i, r in enumerate(buy_reasons) if '入场' in r]
        add_position = [i for i, r in enumerate(buy_reasons) if '加仓' in r]

        if first_entry:
            ax2.scatter([buy_dates[i] for i in first_entry],
                       [buy_prices[i] for i in first_entry],
                       color='green', marker='^', s=120, label='首次入场', zorder=5)
        if add_position:
            ax2.scatter([buy_dates[i] for i in add_position],
                       [buy_prices[i] for i in add_position],
                       color='lightgreen', marker='^', s=80, label='加仓', zorder=5)

    if sell_trades:
        sell_dates = [t['date'] for t in sell_trades]
        sell_prices = [t['price'] for t in sell_trades]
        sell_reasons = [t.get('reason', '') for t in sell_trades]

        # 区分不同出场原因
        stop_loss = [i for i, r in enumerate(sell_reasons) if '止损' in r]
        normal_exit = [i for i, r in enumerate(sell_reasons) if '出场' in r]

        if stop_loss:
            ax2.scatter([sell_dates[i] for i in stop_loss],
                       [sell_prices[i] for i in stop_loss],
                       color='red', marker='v', s=120, label='止损', zorder=5)
        if normal_exit:
            ax2.scatter([sell_dates[i] for i in normal_exit],
                       [sell_prices[i] for i in normal_exit],
                       color='orange', marker='v', s=100, label='正常出场', zorder=5)

    ax2.set_title('股价与交易信号', fontsize=14)
    ax2.set_xlabel('日期')
    ax2.set_ylabel('价格 (元)')
    ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    main()